#!/usr/bin/env python3
"""
语音ROM系统编译测试脚本
用于验证修复后的代码是否能正常编译
"""

import os
import sys
import subprocess

def print_header(title):
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_step(step, description):
    print(f"\n[步骤 {step}] {description}")
    print("-" * 40)

def run_command(cmd, description, show_output=False):
    """运行命令并返回结果"""
    print(f"执行: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, 
                              text=True, timeout=300, cwd=".")
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            if show_output and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                if len(lines) > 5:
                    print("输出 (最后5行):")
                    for line in lines[-5:]:
                        print(f"  {line}")
                else:
                    print("输出:")
                    for line in lines:
                        print(f"  {line}")
            return True
        else:
            print(f"❌ {description} - 失败 (返回码: {result.returncode})")
            if result.stderr.strip():
                print("错误信息:")
                for line in result.stderr.strip().split('\n')[-10:]:  # 只显示最后10行
                    print(f"  {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {description} - 超时")
        return False
    except Exception as e:
        print(f"❌ {description} - 异常: {e}")
        return False

def main():
    print_header("语音ROM系统编译测试")
    
    # 检查PlatformIO
    print_step(1, "检查PlatformIO环境")
    if not run_command("pio --version", "检查PlatformIO版本"):
        print("❌ PlatformIO不可用，请先安装")
        return False
    
    # 清理构建
    print_step(2, "清理之前的构建")
    run_command("pio run -e esp32-s3-devkitc-1-N16R8 -t clean", "清理构建")
    
    # 构建ROM文件
    print_step(3, "构建语音ROM文件")
    if not run_command("python src/scripts/build_voice_rom.py --project-dir .", "构建ROM"):
        print("⚠️ ROM构建失败，继续编译测试...")
    
    # 编译项目
    print_step(4, "编译项目")
    if not run_command("pio run -e esp32-s3-devkitc-1-N16R8", "编译项目", True):
        print("\n❌ 编译失败！")
        return False
    
    # 检查生成文件
    print_step(5, "检查生成的文件")
    
    firmware_file = ".pio/build/esp32-s3-devkitc-1-N16R8/firmware.bin"
    if os.path.exists(firmware_file):
        size = os.path.getsize(firmware_file)
        print(f"✅ 固件生成成功: {size} bytes")
    else:
        print("❌ 固件文件未生成")
        return False
    
    print_header("编译测试完成")
    print("🎉 所有测试通过！代码修复成功。")
    print("\n📋 下一步:")
    print("1. pio run -e esp32-s3-devkitc-1-N16R8 -t uploadfs  # 上传文件系统")
    print("2. pio run -e esp32-s3-devkitc-1-N16R8 -t upload    # 上传固件")
    print("3. pio device monitor -e esp32-s3-devkitc-1-N16R8   # 监控串口")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
